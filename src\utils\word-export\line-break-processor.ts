import { StyleParser } from './style-parser';

/**
 * 换行处理器 - 按照正确的HTML/CSS解析规则处理换行逻辑
 */
export class LineBreakProcessor {
  /**
   * 获取元素的实际类型（考虑display属性和父级flex容器的影响）
   */
  static getActualElementType(
    el: Element
  ): 'inline' | 'block' | 'inline-block' {
    const style = StyleParser.parseStyle(el);

    // 检查父级是否是flex容器
    const isDirectChildOfFlexContainer = this.isDirectChildOfFlexContainer(el);

    if (isDirectChildOfFlexContainer) {
      // 父级是flex容器，所有直接子元素按内联处理
      return 'inline';
    }

    // 如果有display属性，优先使用display属性
    if (style.display === 'inline') return 'inline';
    if (style.display === 'block') return 'block';
    if (style.display === 'inline-block') return 'inline-block';
    if (style.display === 'inline-flex') return 'inline';
    if (style.display === 'flex') return 'block';

    // 否则使用元素的默认类型
    const inlineElements = ['SPAN', 'A', 'STRONG', 'EM', 'B', 'I', 'U', 'IMG'];
    const blockElements = [
      'DIV',
      'P',
      'H1',
      'H2',
      'H3',
      'H4',
      'H5',
      'H6',
      'UL',
      'OL',
      'LI',
    ];

    if (inlineElements.includes(el.tagName)) return 'inline';
    if (blockElements.includes(el.tagName)) return 'block';

    return 'inline'; // 默认为内联
  }

  /**
   * 判断元素是否应该作为段落处理
   */
  static shouldTreatAsParagraph(el: Element): boolean {
    const elementType = this.getActualElementType(el);

    // 只有块级元素才可能作为段落处理
    if (elementType !== 'block') {
      return false;
    }

    // 检查是否在inline-block容器中
    const isInInlineBlockContainer = this.isInInlineBlockContainer(el);

    // 标准段落标签
    if (el.tagName === 'P' || el.tagName.match(/^H[1-6]$/)) {
      // 如果在inline-block容器中，不作为段落处理，而是作为内联内容
      if (isInInlineBlockContainer) {
        return false;
      }
      return true;
    }

    // DIV元素需要特殊判断
    if (el.tagName === 'DIV') {
      // 特殊情况：如果是"序号 + inline-block内容"的左右布局结构
      // 这种情况应该作为段落处理，而不是递归处理
      if (this.isSequenceNumberWithInlineBlockContent(el)) {
        return true;
      }

      // 如果包含inline-block子元素，不作为段落处理（需要递归处理）
      if (this.containsInlineBlockChildren(el)) {
        return false;
      }

      // 如果包含文本内容或主要是内联内容，作为段落处理
      return this.containsParagraphLikeContent(el);
    }

    return false;
  }

  /**
   * 判断元素是否应该递归处理
   */
  static shouldRecurse(el: Element): boolean {
    // 只有在确实需要递归的情况下才递归
    // 大多数情况下，元素应该按照自己的类型处理，而不是递归

    // 如果元素包含多个不同类型的子元素，可能需要递归
    if (el.children.length > 1) {
      const childTypes = Array.from(el.children).map(child =>
        this.getActualElementType(child as Element)
      );

      // 如果包含混合的块级和内联元素，需要递归处理
      const hasBlock = childTypes.includes('block');
      const hasInline =
        childTypes.includes('inline') || childTypes.includes('inline-block');

      if (hasBlock && hasInline) {
        return true;
      }
    }

    return false;
  }

  /**
   * 检查元素是否在flex容器中（只影响直接子元素）
   */
  static isDirectChildOfFlexContainer(el: Element): boolean {
    if (!el.parentElement) return false;

    const parentStyle = StyleParser.parseStyle(el.parentElement);
    return (
      parentStyle.display === 'flex' || parentStyle.display === 'inline-flex'
    );
  }

  /**
   * 检查元素是否应该在前面添加换行
   */
  static shouldAddLineBreakBefore(el: Element): boolean {
    // 获取元素的实际类型
    const actualType = this.getActualElementType(el);

    // 如果是flex容器的直接子元素，按内联处理，不添加换行
    if (this.isDirectChildOfFlexContainer(el)) {
      return false;
    }

    // 只有块级元素才可能需要添加换行
    if (actualType !== 'block') {
      return false;
    }

    // 特殊情况：inline-block容器中的块级元素
    const isInInlineBlockContainer = this.isInInlineBlockContainer(el);

    if (isInInlineBlockContainer) {
      // 检查是否是容器中的第一个块级元素
      const parent = el.parentElement!;
      const blockSiblings = Array.from(parent.children).filter(child => {
        const childType = this.getActualElementType(child as Element);
        return childType === 'block';
      });

      // 如果是第一个块级元素，不添加换行（保持左右结构）
      if (blockSiblings[0] === el) {
        return false;
      }

      // 如果不是第一个块级元素，添加换行（支持内部换行）
      return true;
    }

    return true;
  }

  /**
   * 检查元素是否应该在后面添加换行
   */
  static shouldAddLineBreakAfter(el: Element): boolean {
    // 获取元素的实际类型
    const actualType = this.getActualElementType(el);

    // 如果是flex容器的直接子元素，按内联处理，不添加换行
    if (this.isDirectChildOfFlexContainer(el)) {
      return false;
    }

    // 只有块级元素才可能需要添加换行
    if (actualType !== 'block') {
      return false;
    }

    // 特殊情况：inline-block容器中的块级元素
    const isInInlineBlockContainer = this.isInInlineBlockContainer(el);

    if (isInInlineBlockContainer) {
      // 检查是否是容器中的最后一个块级元素
      const parent = el.parentElement!;
      const blockSiblings = Array.from(parent.children).filter(child => {
        const childType = this.getActualElementType(child as Element);
        return childType === 'block';
      });

      // 如果是最后一个块级元素，不添加换行（保持左右结构）
      if (blockSiblings[blockSiblings.length - 1] === el) {
        return false;
      }

      // 如果不是最后一个块级元素，添加换行（支持内部换行）
      return true;
    }

    return true;
  }

  /**
   * 检查是否是"序号 + inline-block内容"的左右布局结构
   */
  static isSequenceNumberWithInlineBlockContent(el: Element): boolean {
    // 检查是否包含inline-block子元素
    if (!this.containsInlineBlockChildren(el)) {
      return false;
    }

    // 检查子元素结构：应该有内联元素（序号）+ inline-block元素（内容）
    const children = Array.from(el.children);
    if (children.length !== 2) {
      return false;
    }

    // 第一个子元素应该是内联元素（序号）
    const firstChild = children[0] as Element;
    const firstChildType = this.getActualElementType(firstChild);
    if (firstChildType !== 'inline') {
      return false;
    }

    // 第二个子元素应该是inline-block元素（内容）
    const secondChild = children[1] as Element;
    const secondChildStyle = StyleParser.parseStyle(secondChild);
    if (secondChildStyle.display !== 'inline-block') {
      return false;
    }

    return true;
  }

  /**
   * 检查元素是否在inline-block容器中（递归向上查找）
   */
  static isInInlineBlockContainer(el: Element): boolean {
    let current = el.parentElement;

    // 向上查找，直到找到inline-block容器或到达根元素
    while (current) {
      const currentStyle = StyleParser.parseStyle(current);

      // 如果找到inline-block容器，返回true
      if (currentStyle.display === 'inline-block') {
        return true;
      }

      // 如果遇到其他类型的块级容器，停止查找
      // 这样可以避免跨越不相关的容器边界
      if (currentStyle.display === 'block' || current.tagName === 'DIV') {
        // 但是如果这个DIV本身包含inline-block子元素，继续向上查找
        if (this.containsInlineBlockChildren(current)) {
          current = current.parentElement;
          continue;
        }
        break;
      }

      current = current.parentElement;
    }

    return false;
  }

  /**
   * 检查元素是否包含inline-block子元素
   */
  static containsInlineBlockChildren(el: Element): boolean {
    return Array.from(el.children).some(child => {
      const childStyle = StyleParser.parseStyle(child as Element);
      return childStyle.display === 'inline-block';
    });
  }

  /**
   * 检查元素是否包含深层inline-block元素
   */
  static hasInlineBlockDescendants(el: Element): boolean {
    for (const child of Array.from(el.children)) {
      const childStyle = StyleParser.parseStyle(child);
      if (childStyle.display === 'inline-block') {
        return true;
      }
      // 递归检查子元素
      if (this.hasInlineBlockDescendants(child)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 检查元素是否包含块级元素
   */
  static containsBlockElements(el: Element): boolean {
    return (
      el.querySelectorAll('h1, h2, h3, h4, h5, h6, p, table, ul, ol').length > 0
    );
  }

  /**
   * 检查DIV是否包含段落类内容
   */
  static containsParagraphLikeContent(el: Element): boolean {
    // 检查是否包含直接文本内容
    const containsDirectTextNode = Array.from(el.childNodes).some(
      child =>
        child.nodeType === 3 && // TEXT_NODE
        child.textContent?.trim()
    );

    if (containsDirectTextNode) {
      return true;
    }

    // 检查是否主要包含内联内容
    return Array.from(el.children).some(childEl => {
      const childStyle = StyleParser.parseStyle(childEl as Element);
      return (
        (childEl as Element).tagName === 'SPAN' ||
        (childEl as Element).tagName === 'B' ||
        (childEl as Element).tagName === 'STRONG' ||
        (childEl as Element).tagName === 'I' ||
        (childEl as Element).tagName === 'EM' ||
        (childEl as Element).tagName === 'U' ||
        (childEl as Element).tagName === 'A' ||
        (childEl as Element).tagName === 'IMG' ||
        childStyle.display === 'inline' ||
        childStyle.display === 'inline-block'
      );
    });
  }

  /**
   * 获取元素的处理分支类型（用于调试）
   */
  static getProcessingBranch(el: Element): string {
    const elementType = this.getActualElementType(el);

    if (this.shouldTreatAsParagraph(el)) {
      return 'paragraph';
    }

    if (this.shouldRecurse(el)) {
      return 'recurse';
    }

    if (elementType === 'inline-block') {
      return 'inline-block';
    }

    if (elementType === 'inline') {
      return 'inline';
    }

    return 'block';
  }
}
