import { WordExportService } from '../service/word-export.service';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Word导出示例
 * 展示如何在项目中使用WordExportService
 */
async function main() {
  try {
    console.log('开始执行Word导出示例...');

    // 创建WordExportService实例
    const wordExportService = new WordExportService();

    // 读取HTML文件内容
    const htmlFilePath = path.join(
      __dirname,
      '../../testFiles/input-html-sample.html'
    );
    const htmlContent = fs.readFileSync(htmlFilePath, 'utf8');

    console.log(`已读取HTML文件: ${htmlFilePath}`);

    // 设置导出选项
    const exportOptions = {
      title: '试题导出示例',
      author: '系统导出',
      fileName: '试题导出.docx',
      orientation: 'portrait' as 'portrait' | 'landscape',
    };

    // 设置输出路径
    const outputPath = path.join(
      __dirname,
      '../../testFiles/output-word-result-new.docx'
    );

    // 将HTML内容转换为Word文档并保存
    console.log('开始转换HTML到Word...');
    const savedPath = await wordExportService.exportHtmlToWordFile(
      htmlContent,
      outputPath,
      exportOptions
    );

    console.log(`Word文档保存成功: ${savedPath}`);

    // 也可以获取Word文档的Buffer，用于其他用途
    // const docxBuffer = await wordExportService.exportHtmlToWord(htmlContent, exportOptions);
    // console.log(`生成的Word文档大小: ${docxBuffer.length} 字节`);
  } catch (error) {
    console.error('Word导出示例执行失败:', error);
  }
}

// 仅在直接运行此文件时执行示例
if (require.main === module) {
  main().catch(console.error);
}
