import { StyleParser } from './style-parser';

export class TextProcessor {
  /**
   * 提取文本样式
   */
  static extractTextStyle(el: Element, parentStyle: any = {}): any {
    const style = StyleParser.parseStyle(el);
    // 解析 font-weight
    let fontWeight = style.fontWeight || el.getAttribute('font-weight');
    if (!fontWeight && el.hasAttribute('style')) {
      const fwMatch = el.getAttribute('style')?.match(/font-weight:\s*([^;]+)/);
      if (fwMatch) fontWeight = fwMatch[1];
    }
    // 解析 font-style
    let fontStyle = style.fontStyle || el.getAttribute('font-style');
    if (!fontStyle && el.hasAttribute('style')) {
      const fsMatch = el.getAttribute('style')?.match(/font-style:\s*([^;]+)/);
      if (fsMatch) fontStyle = fsMatch[1];
    }
    // 解析 text-align
    let textAlign = style.textAlign || style.alignment;
    if (!textAlign && el.hasAttribute('style')) {
      const taMatch = el.getAttribute('style')?.match(/text-align:\s*([^;]+)/);
      if (taMatch) {
        textAlign = taMatch[1].trim();
      }
    }

    // 检查父元素的对齐方式
    if (!textAlign) {
      let currentEl = el.parentElement;
      while (currentEl) {
        const parentStyle = StyleParser.parseStyle(currentEl);
        if (parentStyle.textAlign || parentStyle.alignment) {
          textAlign = parentStyle.textAlign || parentStyle.alignment;
          console.log(`[对齐传递] 从父元素${currentEl.tagName}继承对齐方式: ${textAlign}`);
          break;
        }
        currentEl = currentEl.parentElement;
      }
    }

    if (textAlign) {
      console.log(`[对齐确认] 元素${el.tagName}最终对齐方式: ${textAlign}`);
    }

    // 解析 font-size
    let fontSize = style.fontSize;
    if (!fontSize && el.hasAttribute('style')) {
      const fsMatch = el.getAttribute('style')?.match(/font-size:\s*(\d+(?:\.\d+)?)(px|pt|em|rem|%)?/);
      if (fsMatch) {
        const size = parseFloat(fsMatch[1]);
        const unit = fsMatch[2] || 'px';

        // 转换为Word的半点单位（1pt = 2半点）
        let sizeInHalfPoints = 24; // 默认12pt

        switch (unit) {
          case 'pt':
            sizeInHalfPoints = size * 2;
            break;
          case 'px':
            // 1px ≈ 0.75pt
            sizeInHalfPoints = (size * 0.75) * 2;
            break;
          case 'em':
          case 'rem':
            // 假设基础字体大小为12pt
            sizeInHalfPoints = (size * 12) * 2;
            break;
          case '%':
            // 假设基础字体大小为12pt
            sizeInHalfPoints = ((size / 100) * 12) * 2;
            break;
          default:
            // 无单位时，假设为px
            sizeInHalfPoints = (size * 0.75) * 2;
        }

        fontSize = Math.round(sizeInHalfPoints);
      }
    }

    // 处理H标签的默认字体大小和加粗
    let defaultFontSize = fontSize || parentStyle.fontSize;
    let isHeadingBold = false;
    if (el.tagName && el.tagName.match(/^H[1-6]$/)) {
      const level = parseInt(el.tagName.substring(1), 10);
      isHeadingBold = true; // 所有H标签都默认加粗
      if (!defaultFontSize) {
        // H标签的默认字体大小（以半点为单位，1pt = 2半点）
        switch (level) {
          case 1: defaultFontSize = 32; break; // 16pt
          case 2: defaultFontSize = 28; break; // 14pt
          case 3: defaultFontSize = 26; break; // 13pt
          case 4: defaultFontSize = 24; break; // 12pt
          case 5: defaultFontSize = 22; break; // 11pt
          case 6: defaultFontSize = 20; break; // 10pt
          default: defaultFontSize = 24; break; // 12pt
        }
      }
      console.log(`[H${level}样式处理] 字体大小: ${defaultFontSize} 加粗: ${isHeadingBold}`);
    }

    return {
      bold:
        parentStyle.bold ||
        isHeadingBold ||
        el.tagName === 'B' ||
        el.tagName === 'STRONG' ||
        fontWeight === 'bold' ||
        fontWeight === '700',
      italics:
        parentStyle.italics ||
        el.tagName === 'I' ||
        el.tagName === 'EM' ||
        fontStyle === 'italic',
      underline:
        parentStyle.underline ||
        el.tagName === 'U' ||
        (style.textDecoration && style.textDecoration.includes('underline')),
      color: style.color ? style.color.replace(/^#/, '') : parentStyle.color,
      fontSize: defaultFontSize,
      alignment: textAlign || parentStyle.alignment,
      emphasisMark: StyleParser.mapTextEmphasisToDocx(
        style.textEmphasis,
        style.textEmphasisPosition
      ),
    };
  }

  /**
   * 向上递归查找父节点（第一个DIV时停止）
   */
  static getParentTagNamesUntilDiv(node: Node): string[] {
    const tagNames: string[] = [];
    let current = node.parentNode as Element | null;
    while (current && current.tagName !== 'DIV') {
      tagNames.push(current.tagName);
      current = current.parentNode as Element | null;
    }
    return tagNames;
  }
}
