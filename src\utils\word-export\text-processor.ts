import { StyleParser } from './style-parser';

export class TextProcessor {
  /**
   * 提取文本样式
   */
  static extractTextStyle(el: Element, parentStyle: any = {}): any {
    const style = StyleParser.parseStyle(el);
    // 解析 font-weight
    let fontWeight = style.fontWeight || el.getAttribute('font-weight');
    if (!fontWeight && el.hasAttribute('style')) {
      const fwMatch = el.getAttribute('style')?.match(/font-weight:\s*([^;]+)/);
      if (fwMatch) fontWeight = fwMatch[1];
    }
    // 解析 font-style
    let fontStyle = style.fontStyle || el.getAttribute('font-style');
    if (!fontStyle && el.hasAttribute('style')) {
      const fsMatch = el.getAttribute('style')?.match(/font-style:\s*([^;]+)/);
      if (fsMatch) fontStyle = fsMatch[1];
    }
    // 解析 text-align
    let textAlign = style.textAlign;
    if (!textAlign && el.hasAttribute('style')) {
      const taMatch = el.getAttribute('style')?.match(/text-align:\s*([^;]+)/);
      if (taMatch) textAlign = taMatch[1];
    }

    // 解析 font-size
    let fontSize = style.fontSize;
    if (!fontSize && el.hasAttribute('style')) {
      const fsMatch = el.getAttribute('style')?.match(/font-size:\s*(\d+(?:\.\d+)?)(px|pt|em|rem|%)?/);
      if (fsMatch) {
        const size = parseFloat(fsMatch[1]);
        const unit = fsMatch[2] || 'px';

        // 转换为Word的半点单位（1pt = 2半点）
        let sizeInHalfPoints = 24; // 默认12pt

        switch (unit) {
          case 'pt':
            sizeInHalfPoints = size * 2;
            break;
          case 'px':
            // 1px ≈ 0.75pt
            sizeInHalfPoints = (size * 0.75) * 2;
            break;
          case 'em':
          case 'rem':
            // 假设基础字体大小为12pt
            sizeInHalfPoints = (size * 12) * 2;
            break;
          case '%':
            // 假设基础字体大小为12pt
            sizeInHalfPoints = ((size / 100) * 12) * 2;
            break;
          default:
            // 无单位时，假设为px
            sizeInHalfPoints = (size * 0.75) * 2;
        }

        fontSize = Math.round(sizeInHalfPoints);
      }
    }

    return {
      bold:
        parentStyle.bold ||
        el.tagName === 'B' ||
        el.tagName === 'STRONG' ||
        fontWeight === 'bold' ||
        fontWeight === '700',
      italics:
        parentStyle.italics ||
        el.tagName === 'I' ||
        el.tagName === 'EM' ||
        fontStyle === 'italic',
      underline:
        parentStyle.underline ||
        el.tagName === 'U' ||
        (style.textDecoration && style.textDecoration.includes('underline')),
      color: style.color ? style.color.replace(/^#/, '') : parentStyle.color,
      fontSize: fontSize || parentStyle.fontSize,
      alignment: textAlign || parentStyle.alignment,
      emphasisMark: StyleParser.mapTextEmphasisToDocx(
        style.textEmphasis,
        style.textEmphasisPosition
      ),
    };
  }

  /**
   * 向上递归查找父节点（第一个DIV时停止）
   */
  static getParentTagNamesUntilDiv(node: Node): string[] {
    const tagNames: string[] = [];
    let current = node.parentNode as Element | null;
    while (current && current.tagName !== 'DIV') {
      tagNames.push(current.tagName);
      current = current.parentNode as Element | null;
    }
    return tagNames;
  }
}
